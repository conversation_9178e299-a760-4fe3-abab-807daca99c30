<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { Search, Plus, Edit, Delete, Phone, Message, Location, User, Calendar, Money, Male, Female, View, Loading, House } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import BackHeader from '@/components/BackHeader.vue';
import { getUserListApi } from '@/request/userApi';
import { getHouseListApi } from '@/request/houseApi';
import { addResidentApi, editResidentApi, deleteResidentApi, getResidentByIdApi, getResidentListApi } from '@/request/residentsApi';

// 搜索关键词
const searchKeyword = ref('');

// 当前页码
const currentPage = ref(1);
// 每页显示条数
const pageSize = ref(10);
// 总条数
const total = ref(0);
// 筛选条件
const filterValue = ref('全部状态');

// 住户数据
const residents = reactive([]);
const loadingResidents = ref(false);

// 用户选项数据
const userOptions = reactive([]);
const loadingUsers = ref(false);

// 房产选项数据
const houseOptions = reactive([]);
const loadingHouses = ref(false);

// 添加住户对话框相关
const addDialogVisible = ref(false);
const addFormRef = ref(null);
const isSubmitting = ref(false);

// 查看住户详情对话框相关
const viewDialogVisible = ref(false);
const viewResidentData = reactive({});

// 编辑住户对话框相关
const editDialogVisible = ref(false);
const editFormRef = ref(null);
const isEditSubmitting = ref(false);
const editResidentData = reactive({
  residentId: '',
  userId: '',
  houseId: '',
  name: '',
  phone: '',
  email: '',
  status: '在住',
  moveInDate: '',
  endDate: '',
  rent: '',
  deposit: ''
});

// 新住户表单数据
const newResident = reactive({
  userId: '',
  houseId: '',
  name: '',
  phone: '',
  email: '',
  status: '在住',
  moveInDate: '',
  endDate: '',
  rent: '',
  deposit: ''
});

// 表单验证规则
const rules = {
  userId: [
    { required: true, message: '请选择用户', trigger: 'change' }
  ],
  houseId: [
    { required: true, message: '请选择房产', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入住户姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  moveInDate: [
    { required: true, message: '请选择入住日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择到期日期', trigger: 'change' }
  ],
  rent: [
    { required: true, message: '请输入租金', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额', trigger: 'blur' }
  ],
  deposit: [
    { required: true, message: '请输入押金', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 数据转换函数
const getStatusLabel = (status) => {
  const statusMap = {
    '0': '在住',
    '1': '即将到期',
    '2': '已退租'
  };
  return statusMap[status] || status;
};

const getStatusClass = (status) => {
  const classMap = {
    '在住': 'status-active',
    '即将到期': 'status-warning',
    '已退租': 'status-inactive'
  };
  return classMap[status] || '';
};

const getGenderText = (gender) => {
  return gender === '0' ? '男' : '女';
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN');
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadResidentList();
};

// 处理筛选
const handleFilter = (filterItem) => {
  filterValue.value = filterItem;
  currentPage.value = 1;
  loadResidentList();
};

// 获取用户列表
const loadUserOptions = async () => {
  try {
    loadingUsers.value = true;
    const response = await getUserListApi({
      pageNum: 1,
      pageSize: 1000,
      status: '1'
    });

    if (response.code === 200 && response.data) {
      userOptions.length = 0;
      response.data.forEach(user => {
        userOptions.push({
          value: user.userId,
          label: user.nickname || user.username
        });
      });
    } else {
      ElMessage.error('获取用户列表失败');
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    ElMessage.error('获取用户列表失败，请稍后重试');
  } finally {
    loadingUsers.value = false;
  }
};

// 获取房产列表
const loadHouseOptions = async () => {
  try {
    loadingHouses.value = true;
    const response = await getHouseListApi({
      pageNum: 1,
      pageSize: 1000,
      searchKey: '',
      building: ''
    });

    if (response.code === 200 && response.data) {
      houseOptions.length = 0;
      response.data.forEach(house => {
        houseOptions.push({
          value: house.houseId || house.id,
          label: `${house.building} ${house.room}`
        });
      });
    } else {
      ElMessage.error('获取房产列表失败');
    }
  } catch (error) {
    console.error('获取房产列表失败:', error);
    ElMessage.error('获取房产列表失败，请稍后重试');
  } finally {
    loadingHouses.value = false;
  }
};

// 获取住户列表
const loadResidentList = async () => {
  try {
    loadingResidents.value = true;
    const response = await getResidentListApi({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      searchKey: searchKeyword.value || undefined,
      status: filterValue.value === '全部状态' ? undefined : filterValue.value
    });

    if (response.code === 200 && response.data) {
      residents.length = 0;
      response.data.forEach(resident => {
        residents.push({
          id: resident.residentId || resident.id,
          residentId: resident.residentId || resident.id,
          userId: resident.userId,
          houseId: resident.houseId,
          name: resident.name,
          phone: resident.phone,
          email: resident.email,
          status: getStatusLabel(resident.status),
          moveInDate: formatDate(resident.moveInDate),
          endDate: formatDate(resident.endDate),
          rent: `¥${resident.rent}`,
          deposit: `押金 ¥${resident.deposit}`,
          building: resident.building || '--',
          room: resident.room || '--'
        });
      });

      total.value = response.total || residents.length;
    } else {
      ElMessage.error('获取住户列表失败');
    }
  } catch (error) {
    console.error('获取住户列表失败:', error);
    ElMessage.error('获取住户列表失败，请稍后重试');
  } finally {
    loadingResidents.value = false;
  }
};

// 锁定页面滚动
const lockBodyScroll = () => {
  const scrollY = window.scrollY;
  document.documentElement.classList.add('dialog-open');
  document.body.classList.add('dialog-open');
  document.body.style.overflow = 'hidden';
  document.body.style.position = 'fixed';
  document.body.style.top = `-${scrollY}px`;
  document.body.style.left = '0';
  document.body.style.right = '0';
  document.body.style.width = '100%';
  document.body.style.height = '100%';
  document.body.dataset.scrollY = scrollY.toString();
};

// 解锁页面滚动
const unlockBodyScroll = () => {
  const scrollY = parseInt(document.body.dataset.scrollY || '0');
  document.documentElement.classList.remove('dialog-open');
  document.body.classList.remove('dialog-open');
  document.body.style.overflow = '';
  document.body.style.position = '';
  document.body.style.top = '';
  document.body.style.left = '';
  document.body.style.right = '';
  document.body.style.width = '';
  document.body.style.height = '';
  delete document.body.dataset.scrollY;
  window.scrollTo(0, scrollY);
};

// 添加新住户
const addNewResident = async () => {
  addDialogVisible.value = true;
  lockBodyScroll();

  // 加载选项数据
  await Promise.all([loadUserOptions(), loadHouseOptions()]);

  // 重置表单
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  // 重置表单数据
  Object.assign(newResident, {
    userId: '',
    houseId: '',
    name: '',
    phone: '',
    email: '',
    status: '在住',
    moveInDate: '',
    endDate: '',
    rent: '',
    deposit: ''
  });
};

// 提交新住户表单
const submitNewResident = async () => {
  if (!addFormRef.value) return;

  await addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        isSubmitting.value = true;

        const response = await addResidentApi({
          userId: parseInt(newResident.userId),
          houseId: parseInt(newResident.houseId),
          name: newResident.name,
          phone: newResident.phone,
          email: newResident.email,
          status: newResident.status,
          moveInDate: new Date(newResident.moveInDate),
          endDate: new Date(newResident.endDate),
          rent: parseFloat(newResident.rent),
          deposit: parseFloat(newResident.deposit),
          createTime: new Date(),
          updateTime: new Date()
        });

        if (response.code === 200) {
          ElMessage.success('添加住户成功');
          addDialogVisible.value = false;
          unlockBodyScroll();
          await loadResidentList();
        } else {
          ElMessage.error(response.msg || '添加住户失败');
        }
      } catch (error) {
        console.error('添加住户失败:', error);
        ElMessage.error('添加住户失败，请稍后重试');
      } finally {
        isSubmitting.value = false;
      }
    }
  });
};

// 取消添加
const cancelAdd = () => {
  addDialogVisible.value = false;
  unlockBodyScroll();
};

// 查看住户详情
const viewResidentDetail = async (resident) => {
  try {
    const response = await getResidentByIdApi(resident.residentId || resident.id);
    if (response.code === 200 && response.data) {
      Object.assign(viewResidentData, {
        ...response.data,
        statusLabel: getStatusLabel(response.data.status),
        moveInDateFormatted: formatDate(response.data.moveInDate),
        endDateFormatted: formatDate(response.data.endDate)
      });
      viewDialogVisible.value = true;
      lockBodyScroll();
    } else {
      ElMessage.error('获取住户详情失败');
    }
  } catch (error) {
    console.error('获取住户详情失败:', error);
    ElMessage.error('获取住户详情失败，请稍后重试');
  }
};

// 关闭查看详情对话框
const closeViewDialog = () => {
  viewDialogVisible.value = false;
  unlockBodyScroll();
};

// 编辑住户
const editResident = async (resident) => {
  try {
    // 先加载选项数据
    await Promise.all([loadUserOptions(), loadHouseOptions()]);

    const response = await getResidentByIdApi(resident.residentId || resident.id);
    if (response.code === 200 && response.data) {
      Object.assign(editResidentData, {
        residentId: response.data.residentId || response.data.id,
        userId: response.data.userId,
        houseId: response.data.houseId,
        name: response.data.name,
        phone: response.data.phone,
        email: response.data.email,
        status: response.data.status,
        moveInDate: response.data.moveInDate ? response.data.moveInDate.split('T')[0] : '',
        endDate: response.data.endDate ? response.data.endDate.split('T')[0] : '',
        rent: response.data.rent,
        deposit: response.data.deposit
      });

      editDialogVisible.value = true;
      lockBodyScroll();

      // 重置表单验证状态
      if (editFormRef.value) {
        editFormRef.value.clearValidate();
      }
    } else {
      ElMessage.error('获取住户信息失败');
    }
  } catch (error) {
    console.error('获取住户信息失败:', error);
    ElMessage.error('获取住户信息失败，请稍后重试');
  }
};

// 提交编辑住户表单
const submitEditResident = async () => {
  if (!editFormRef.value) return;

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        isEditSubmitting.value = true;

        const response = await editResidentApi({
          residentId: parseInt(editResidentData.residentId),
          userId: parseInt(editResidentData.userId),
          houseId: parseInt(editResidentData.houseId),
          name: editResidentData.name,
          phone: editResidentData.phone,
          email: editResidentData.email,
          status: editResidentData.status,
          moveInDate: new Date(editResidentData.moveInDate),
          endDate: new Date(editResidentData.endDate),
          rent: parseFloat(editResidentData.rent),
          deposit: parseFloat(editResidentData.deposit)
        });

        if (response.code === 200) {
          ElMessage.success('编辑住户成功');
          editDialogVisible.value = false;
          unlockBodyScroll();
          await loadResidentList();
        } else {
          ElMessage.error(response.msg || '编辑住户失败');
        }
      } catch (error) {
        console.error('编辑住户失败:', error);
        ElMessage.error('编辑住户失败，请稍后重试');
      } finally {
        isEditSubmitting.value = false;
      }
    }
  });
};

// 取消编辑
const cancelEdit = () => {
  editDialogVisible.value = false;
  unlockBodyScroll();
};

// 删除住户
const deleteResident = async (resident) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除住户 "${resident.name}" 吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    const response = await deleteResidentApi(resident.residentId || resident.id);
    if (response.code === 200) {
      ElMessage.success('删除住户成功');
      await loadResidentList();
    } else {
      ElMessage.error(response.msg || '删除住户失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除住户失败:', error);
      ElMessage.error('删除住户失败，请稍后重试');
    }
  }
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadResidentList();
};

// 监听对话框关闭事件
watch([addDialogVisible, editDialogVisible, viewDialogVisible], ([add, edit, view]) => {
  if (!add && !edit && !view) {
    // 移除滚动事件监听器
    window.removeEventListener('scroll', preventScroll);
    unlockBodyScroll();
  }
});

// 阻止滚动事件
const preventScroll = (e) => {
  e.preventDefault();
  e.stopPropagation();
  return false;
};

// 组件挂载时加载数据
onMounted(() => {
  loadResidentList();
});



</script>

<template>
  <BackHeader activeMenu="residents">
    <div class="residents-container">
      <!-- 页面标题和添加按钮 -->
      <div class="page-header">
        <div class="title-section">
          <h1 class="page-title">住户管理</h1>
          <p class="page-subtitle">管理小区住户和租赁信息</p>
        </div>

        <el-button type="primary" class="add-resident-btn" @click="addNewResident">
          <el-icon><Plus /></el-icon>
          添加住户
        </el-button>
      </div>

      <!-- 添加住户对话框 -->
      <el-dialog
        v-model="addDialogVisible"
        title="添加新住户"
        width="800px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        class="add-resident-dialog"
      >
        <div class="dialog-content">
          <el-form
            ref="addFormRef"
            :model="newResident"
            :rules="rules"
            label-width="100px"
            label-position="right"
            class="resident-form"
          >
            <!-- 基本信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><User /></el-icon>
                基本信息
              </h3>

              <el-form-item label="用户" prop="userId">
                <el-select
                  v-model="newResident.userId"
                  placeholder="请选择用户"
                  style="width: 100%"
                  clearable
                  :loading="loadingUsers"
                  loading-text="加载用户列表中..."
                  no-data-text="暂无用户数据"
                >
                  <el-option
                    v-for="user in userOptions"
                    :key="user.value"
                    :label="user.label"
                    :value="user.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="房产" prop="houseId">
                <el-select
                  v-model="newResident.houseId"
                  placeholder="请选择房产"
                  style="width: 100%"
                  clearable
                  :loading="loadingHouses"
                  loading-text="加载房产列表中..."
                  no-data-text="暂无房产数据"
                >
                  <el-option
                    v-for="house in houseOptions"
                    :key="house.value"
                    :label="house.label"
                    :value="house.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model="newResident.name"
                  placeholder="请输入住户姓名"
                />
              </el-form-item>

              <el-form-item label="联系电话" prop="phone">
                <el-input
                  v-model="newResident.phone"
                  placeholder="请输入手机号码"
                />
              </el-form-item>

              <el-form-item label="邮箱" prop="email">
                <el-input
                  v-model="newResident.email"
                  placeholder="请输入邮箱地址"
                />
              </el-form-item>

              <el-form-item label="住户状态" prop="status">
                <el-select
                  v-model="newResident.status"
                  placeholder="请选择住户状态"
                  style="width: 100%"
                >
                  <el-option label="在住" value="0" />
                  <el-option label="即将到期" value="1" />
                  <el-option label="已退租" value="2" />
                </el-select>
              </el-form-item>
            </div>

            <!-- 租期信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><Calendar /></el-icon>
                租期信息
              </h3>

              <el-form-item label="入住日期" prop="moveInDate">
                <el-date-picker
                  v-model="newResident.moveInDate"
                  type="date"
                  placeholder="选择入住日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>

              <el-form-item label="到期日期" prop="endDate">
                <el-date-picker
                  v-model="newResident.endDate"
                  type="date"
                  placeholder="选择到期日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </div>

            <!-- 费用信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><Money /></el-icon>
                费用信息
              </h3>

              <el-form-item label="月租金" prop="rent">
                <el-input
                  v-model="newResident.rent"
                  placeholder="请输入月租金"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>

              <el-form-item label="押金" prop="deposit">
                <el-input
                  v-model="newResident.deposit"
                  placeholder="请输入押金金额"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </div>

          </el-form>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelAdd">取消</el-button>
            <el-button
              type="primary"
              @click="submitNewResident"
              :loading="isSubmitting"
            >
              {{ isSubmitting ? '添加中...' : '确认添加' }}
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 查看住户详情对话框 -->
      <el-dialog
        v-model="viewDialogVisible"
        title="住户详情"
        width="600px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        class="view-resident-dialog"
      >
        <div class="view-content">
          <div class="detail-section">
            <h3 class="section-title">
              <el-icon><User /></el-icon>
              基本信息
            </h3>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="detail-label">姓名：</span>
                <span class="detail-value">{{ viewResidentData.name }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">联系电话：</span>
                <span class="detail-value">{{ viewResidentData.phone }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">邮箱：</span>
                <span class="detail-value">{{ viewResidentData.email || '--' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">状态：</span>
                <el-tag
                  :type="viewResidentData.status === '在住' ? 'success' :
                      viewResidentData.status === '即将到期' ? 'warning' : 'info'"
                  size="small"
                >
                  {{ viewResidentData.statusLabel }}
                </el-tag>
              </div>
              <div class="detail-item">
                <span class="detail-label">入住日期：</span>
                <span class="detail-value">{{ viewResidentData.moveInDateFormatted }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">到期日期：</span>
                <span class="detail-value">{{ viewResidentData.endDateFormatted }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">月租金：</span>
                <span class="detail-value rent-highlight">¥{{ viewResidentData.rent }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">押金：</span>
                <span class="detail-value">¥{{ viewResidentData.deposit }}</span>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="closeViewDialog">关闭</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 编辑住户对话框 -->
      <el-dialog
        v-model="editDialogVisible"
        title="编辑住户"
        width="800px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        class="edit-resident-dialog"
      >
        <div class="dialog-content">
          <el-form
            ref="editFormRef"
            :model="editResidentData"
            :rules="rules"
            label-width="100px"
            label-position="right"
            class="resident-form"
          >
            <!-- 基本信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><User /></el-icon>
                基本信息
              </h3>

              <el-form-item label="用户" prop="userId">
                <el-select
                  v-model="editResidentData.userId"
                  placeholder="请选择用户"
                  style="width: 100%"
                  clearable
                  :loading="loadingUsers"
                  loading-text="加载用户列表中..."
                  no-data-text="暂无用户数据"
                >
                  <el-option
                    v-for="user in userOptions"
                    :key="user.value"
                    :label="user.label"
                    :value="user.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="房产" prop="houseId">
                <el-select
                  v-model="editResidentData.houseId"
                  placeholder="请选择房产"
                  style="width: 100%"
                  clearable
                  :loading="loadingHouses"
                  loading-text="加载房产列表中..."
                  no-data-text="暂无房产数据"
                >
                  <el-option
                    v-for="house in houseOptions"
                    :key="house.value"
                    :label="house.label"
                    :value="house.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model="editResidentData.name"
                  placeholder="请输入住户姓名"
                />
              </el-form-item>

              <el-form-item label="联系电话" prop="phone">
                <el-input
                  v-model="editResidentData.phone"
                  placeholder="请输入手机号码"
                />
              </el-form-item>

              <el-form-item label="邮箱" prop="email">
                <el-input
                  v-model="editResidentData.email"
                  placeholder="请输入邮箱地址"
                />
              </el-form-item>

              <el-form-item label="住户状态" prop="status">
                <el-select
                  v-model="editResidentData.status"
                  placeholder="请选择住户状态"
                  style="width: 100%"
                >
                  <el-option label="在住" value="0" />
                  <el-option label="即将到期" value="1" />
                  <el-option label="已退租" value="2" />
                </el-select>
              </el-form-item>
            </div>

            <!-- 租期信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><Calendar /></el-icon>
                租期信息
              </h3>

              <el-form-item label="入住日期" prop="moveInDate">
                <el-date-picker
                  v-model="editResidentData.moveInDate"
                  type="date"
                  placeholder="选择入住日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>

              <el-form-item label="到期日期" prop="endDate">
                <el-date-picker
                  v-model="editResidentData.endDate"
                  type="date"
                  placeholder="选择到期日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </div>

            <!-- 费用信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><Money /></el-icon>
                费用信息
              </h3>

              <el-form-item label="月租金" prop="rent">
                <el-input
                  v-model="editResidentData.rent"
                  placeholder="请输入月租金"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>

              <el-form-item label="押金" prop="deposit">
                <el-input
                  v-model="editResidentData.deposit"
                  placeholder="请输入押金金额"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </div>

          </el-form>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelEdit">取消</el-button>
            <el-button
              type="primary"
              @click="submitEditResident"
              :loading="isEditSubmitting"
            >
              {{ isEditSubmitting ? '保存中...' : '保存修改' }}
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 搜索和筛选 -->
      <div class="search-filter-bar">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索住户姓名、房间号、联系电话..."
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="filter-box">
          <el-dropdown>
            <el-button class="filter-button">
              {{ filterValue }}
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>全部状态</el-dropdown-item>
                <el-dropdown-item>在住</el-dropdown-item>
                <el-dropdown-item>即将到期</el-dropdown-item>
                <el-dropdown-item>已退租</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 住户列表容器 -->
      <div class="residents-list-container">
        <!-- 添加表头 -->
        <div class="residents-list-header">
          <div class="header-left">
            <div class="header-avatar"></div>
            <div class="header-info">
              <span>住户信息</span>
            </div>
          </div>
          <div class="header-right">
            <div class="header-lease">租期</div>
            <div class="header-payment">费用</div>
            <div class="header-actions">操作</div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loadingResidents" class="loading-container">
          <el-icon class="loading-icon" :size="40">
            <Loading />
          </el-icon>
          <p class="loading-text">加载住户列表中...</p>
        </div>

        <!-- 空状态 -->
        <div v-else-if="residents.length === 0" class="empty-container">
          <el-icon class="empty-icon" :size="60">
            <User />
          </el-icon>
          <p class="empty-text">暂无住户数据</p>
          <el-button type="primary" @click="addNewResident">
            <el-icon><Plus /></el-icon>
            添加住户
          </el-button>
        </div>

        <!-- 住户列表 -->
        <div v-else v-for="resident in residents" :key="resident.id" class="resident-card">
          <div class="resident-card-left">
            <div class="resident-avatar">
              {{ resident.name.charAt(0) }}
            </div>
            <div class="resident-main-info">
              <div class="resident-name-row">
                <h3 class="resident-name">{{ resident.name }}</h3>
                <div class="gender-tag" :class="resident.gender === '0' ? 'gender-male' : 'gender-female'">
                  <el-icon>
                    <Male v-if="resident.gender === '0'" />
                    <Female v-else />
                  </el-icon>
                  {{ getGenderText(resident.gender) }}
                </div>
                <div class="status-tag" :class="getStatusClass(resident.status)">
                  {{ resident.status }}
                </div>
              </div>
              <div class="resident-details">
                <div class="detail-item">
                  <el-icon><Phone /></el-icon>
                  <span>{{ resident.phone }}</span>
                </div>
                <div class="detail-item">
                  <el-icon><Message /></el-icon>
                  <span>{{ resident.email }}</span>
                </div>
                <div class="detail-item">
                  <el-icon><Location /></el-icon>
                  <span>{{ resident.building }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="resident-card-right">
            <div class="lease-dates">
              <div class="date-value">{{ resident.moveInDate }} 至 {{ resident.endDate }}</div>
            </div>

            <div class="lease-payment">
              <div class="rent">{{ resident.rent }}</div>
              <div class="deposit">{{ resident.deposit }}</div>
            </div>

            <div class="action-buttons">
              <el-button
                type="info"
                size="large"
                circle
                @click="viewResidentDetail(resident)"
                title="查看详情"
              >
                <el-icon><View /></el-icon>
              </el-button>
              <el-button
                type="primary"
                size="large"
                circle
                @click="editResident(resident)"
                title="编辑住户"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                type="danger"
                size="large"
                circle
                @click="deleteResident(resident)"
                title="删除住户"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <div class="total-info">共 {{ total }} 条记录</div>
          <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :current-page="currentPage"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </BackHeader>
</template>

<style scoped>
.residents-container {
  padding: 0 20px 20px;
  max-width: 1800px; /* 增加最大宽度 */
  margin: 0 auto;
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px; /* 增大标题字体 */
  font-weight: 600;
  margin: 0;
  color: #333;
}

.page-subtitle {
  font-size: 16px; /* 增大副标题字体 */
  color: #828385;
  margin: 10px 0 0 0; /* 增加上边距 */
}

.add-resident-btn {
  display: flex;
  align-items: center;
  gap: 10px; /* 增加图标和文字间距 */
  height: 48px; /* 增加按钮高度 */
  font-size: 18px; /* 增大按钮字体 */
  padding: 0 20px; /* 增加左右内边距 */
}

.search-filter-bar {
  display: flex;
  margin-bottom: 30px; /* 增加下边距 */
  align-items: center;
}

.search-box {
  width: 50%; /* 搜索框宽度缩短 */
  margin-right: 20px; /* 增加右边距 */
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__inner) {
  height: 52px; /* 增加搜索框高度 */
  font-size: 18px; /* 增大搜索框字体 */
  padding: 0 20px; /* 增加左右内边距 */
}

.filter-button {
  display: flex;
  align-items: center;
  height: 52px; /* 增加按钮高度 */
  font-size: 18px; /* 增大按钮字体 */
  padding: 0 20px; /* 增加左右内边距 */
}

/* 列表容器样式 */
.residents-list-container {
  background-color: #fff;
  border-radius: 12px; /* 增加圆角 */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08); /* 增强阴影效果 */
  padding: 30px; /* 增加内边距 */
}

/* 表头样式 */
.residents-list-header {
  display: flex;
  justify-content: space-between;
  padding: 0 20px 16px 20px; /* 增加内边距 */
  border-bottom: 1px solid #ebeef5;
  color: #606266; /* 颜色加深 */
  font-size: 16px; /* 增大字体 */
  font-weight: 500;
  margin-bottom: 12px; /* 增加下边距 */
}

.header-left {
  display: flex;
  align-items: center;
  flex: 2;
}

.header-avatar {
  width: 60px; /* 增加宽度与头像一致 */
  margin-right: 20px; /* 增加右边距 */
}

.header-right {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 80px; /* 使用更大的固定间距 */
  margin-left: 60px; /* 增加与左侧的间距 */
}

.header-lease, .header-payment, .header-actions {
  flex: 0 0 auto; /* 不伸缩，保持内容宽度 */
  width: 120px; /* 设置固定宽度 */
  text-align: left; /* 左对齐文本 */
}

.header-actions {
  text-align: center; /* 操作列居中对齐 */
}

/* 住户卡片样式 */
.resident-card {
  display: flex;
  justify-content: space-between;
  padding: 24px 20px; /* 增加内边距 */
  border-bottom: 1px solid #f0f0f0;
}

.resident-card:last-child {
  border-bottom: none;
}

.resident-card-left {
  display: flex;
  align-items: center;
  flex: 2;
}

.resident-avatar {
  width: 60px; /* 增加头像尺寸 */
  height: 60px; /* 增加头像尺寸 */
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26px; /* 增大字体 */
  font-weight: bold;
  margin-right: 20px; /* 增加右边距 */
}

.resident-main-info {
  display: flex;
  flex-direction: column;
}

.resident-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.resident-name {
  font-size: 18px; /* 增大字体 */
  font-weight: 500;
  /* 增加右边距 */
  margin: 0 16px 0 0;
}

.resident-details {
  display: flex;
  flex-wrap: wrap;
  gap: 16px; /* 增加间距 */
  margin-top: 10px; /* 增加与名称的间距 */
}

.detail-item {
  display: flex;
  align-items: center;
  font-size: 16px; /* 增大字体 */
  color: #606266;
}

.detail-item .el-icon {
  margin-right: 8px; /* 增加右边距 */
  font-size: 18px; /* 增大图标 */
  color: #909399;
}

.resident-card-right {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 80px; /* 与表头保持一致 */
  margin-left: 60px; /* 与表头保持一致 */
}

.lease-dates, .lease-payment {
  flex: 0 0 auto; /* 不伸缩，保持内容宽度 */
  width: 120px; /* 设置固定宽度 */
}

.rent {
  font-weight: 500;
  color: #606266;
  font-size: 16px; /* 增大字体 */
}

.deposit {
  font-size: 15px; /* 增大字体 */
  color: #909399;
  margin-top: 6px; /* 增加上边距 */
}

.action-buttons {
  flex: 0 0 auto; /* 不伸缩，保持内容宽度 */
  width: 120px; /* 设置固定宽度 */
  display: flex;
  justify-content: center; /* 操作按钮居中 */
  gap: 12px; /* 按钮之间的间距 */
}

.action-buttons .el-button {
  --el-button-size: 40px; /* 增加按钮大小 */
}

.gender-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  margin-right: 8px;
}

.gender-male {
  background-color: #e6f4ff;
  color: #1890ff;
}

.gender-female {
  background-color: #fff0f6;
  color: #eb2f96;
}

.gender-tag .el-icon {
  font-size: 14px;
}

.status-tag {
  display: inline-block;
  padding: 6px 12px; /* 增加内边距 */
  border-radius: 4px;
  font-size: 15px; /* 增大字体 */
  font-weight: 500;
}

.status-active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-expiring {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-inactive {
  background-color: #f5f5f5;
  color: #909399;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px; /* 增加上边距 */
}

.total-info {
  font-size: 18px; /* 增大字体 */
  color: #909399;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .resident-card {
    flex-direction: column;
  }

  .resident-card-right {
    margin-top: 16px;
    justify-content: flex-start;
    gap: 24px;
  }

  .lease-dates, .lease-payment {
    width: auto;
  }

  .search-box {
    width: 70%;
  }
}

@media (max-width: 768px) {
  .search-filter-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    width: 100%;
    margin-right: 0;
    margin-bottom: 12px;
  }

  .resident-details {
    flex-direction: column;
    gap: 8px;
  }
}

/* 调整租期列的文本颜色 */
.lease-dates .date-value {
  color: #909399; /* 使用更浅的灰色 */
  font-size: 15px; /* 保持字体大小适中 */
}

/* 可以同时调整表头颜色保持一致 */
.header-lease {
  color: #606266; /* 表头保持稍深一点的颜色 */
}

/* 添加住户对话框样式 - 完全固定 */
.add-resident-dialog {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 2000 !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

.add-resident-dialog :deep(.el-overlay) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 2000 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

.add-resident-dialog :deep(.el-overlay-dialog) {
  position: fixed !important;
  top: 30% !important;
  left: 50% !important;
  transform: translate(-50%, -30%) !important;
  margin: 0 !important;
  padding: 0 !important;
  max-height: none !important;
  overflow: visible !important;
  width: auto !important;
  height: auto !important;
}

.add-resident-dialog :deep(.el-dialog) {
  position: relative !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  max-height: 70vh !important;
  width: 800px !important;
  max-width: 90vw !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.add-resident-dialog :deep(.el-dialog__header) {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.add-resident-dialog :deep(.el-dialog__title) {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.add-resident-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: calc(70vh - 120px);
  overflow-y: auto;
  flex: 1;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.add-resident-dialog :deep(.el-dialog__body)::-webkit-scrollbar {
  width: 6px;
}

.add-resident-dialog :deep(.el-dialog__body)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.add-resident-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.add-resident-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dialog-content {
  width: 100%;
}

.resident-form {
  width: 100%;
}

.form-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e6f7ff;
}

.section-title .el-icon {
  font-size: 18px;
  color: #1890ff;
}

/* 表单项样式 */
.resident-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.resident-form :deep(.el-form-item__label) {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.resident-form :deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 0 0 1px #d9d9d9;
  transition: all 0.2s;
}

.resident-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #40a9ff;
}

.resident-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.resident-form :deep(.el-input__inner) {
  font-size: 16px;
  color: #333;
}

.resident-form :deep(.el-input-group__prepend),
.resident-form :deep(.el-input-group__append) {
  background-color: #fafafa;
  border-color: #d9d9d9;
  color: #666;
  font-weight: 500;
  font-size: 16px;
}

.resident-form :deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

.resident-form :deep(.el-date-editor) {
  width: 100%;
}

.resident-form :deep(.el-textarea__inner) {
  border-radius: 6px;
  border-color: #d9d9d9;
  font-size: 16px;
  color: #333;
  resize: vertical;
}

.resident-form :deep(.el-textarea__inner:hover) {
  border-color: #40a9ff;
}

.resident-form :deep(.el-textarea__inner:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 对话框底部 */
.add-resident-dialog :deep(.el-dialog__footer) {
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 24px;
  border-top: 1px solid #f0f0f0;
  margin: 0 -24px -24px;
  background-color: #fff;
}

.dialog-footer .el-button {
  min-width: 100px;
  height: 40px;
  font-size: 16px;
  border-radius: 6px;
}

/* 确保对话框始终固定 */
.add-resident-dialog :deep(.el-dialog__wrapper) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* 强制防止页面滚动 */
body.el-popup-parent--hidden,
body:has(.add-resident-dialog) {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

/* 全局阻止滚动 */
html:has(.add-resident-dialog),
html.dialog-open {
  overflow: hidden !important;
  height: 100% !important;
}

body.dialog-open {
  overflow: hidden !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .add-resident-dialog :deep(.el-overlay-dialog) {
    top: 25% !important;
    transform: translate(-50%, -25%) !important;
    padding: 10px !important;
  }

  .add-resident-dialog :deep(.el-dialog) {
    width: 95% !important;
    max-width: 95% !important;
    max-height: 65vh !important;
    margin: 0 !important;
  }

  .form-section {
    padding: 12px;
    margin-bottom: 16px;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 14px;
  }

  .resident-form :deep(.el-col) {
    width: 100% !important;
    margin-bottom: 0;
  }

  .resident-form :deep(.el-form-item) {
    margin-bottom: 12px;
  }

  .add-resident-dialog :deep(.el-dialog__body) {
    max-height: calc(65vh - 100px);
    padding: 16px;
  }
}

@media (max-height: 700px) {
  .add-resident-dialog :deep(.el-overlay-dialog) {
    top: 20% !important;
    transform: translate(-50%, -20%) !important;
  }

  .add-resident-dialog :deep(.el-dialog) {
    max-height: 60vh !important;
  }

  .add-resident-dialog :deep(.el-dialog__body) {
    max-height: calc(60vh - 100px);
    padding: 12px;
  }

  .form-section {
    padding: 10px;
    margin-bottom: 12px;
  }
}

/* 表单验证错误样式 */
.resident-form :deep(.el-form-item.is-error .el-input__wrapper) {
  box-shadow: 0 0 0 1px #ff4d4f;
}

.resident-form :deep(.el-form-item.is-error .el-textarea__inner) {
  border-color: #ff4d4f;
}

.resident-form :deep(.el-form-item__error) {
  font-size: 14px;
  color: #ff4d4f;
  margin-top: 4px;
}

/* 查看住户详情对话框样式 */
.view-resident-dialog :deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.view-resident-dialog :deep(.el-dialog__header) {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.view-resident-dialog :deep(.el-dialog__title) {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.view-resident-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.view-content {
  width: 100%;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px 24px;
  margin-top: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.detail-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  margin-right: 12px;
}

.detail-value {
  color: #333;
  font-weight: 400;
}

.rent-highlight {
  color: #409EFF;
  font-weight: 600;
  font-size: 16px;
}

/* 编辑住户对话框样式 - 继承添加对话框样式 */
.edit-resident-dialog {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 2000 !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

.edit-resident-dialog :deep(.el-overlay) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 2000 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

.edit-resident-dialog :deep(.el-overlay-dialog) {
  position: fixed !important;
  top: 30% !important;
  left: 50% !important;
  transform: translate(-50%, -30%) !important;
  margin: 0 !important;
  padding: 0 !important;
  max-height: none !important;
  overflow: visible !important;
  width: auto !important;
  height: auto !important;
}

.edit-resident-dialog :deep(.el-dialog) {
  position: relative !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  max-height: 70vh !important;
  width: 800px !important;
  max-width: 90vw !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.edit-resident-dialog :deep(.el-dialog__header) {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.edit-resident-dialog :deep(.el-dialog__title) {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.edit-resident-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: calc(70vh - 120px);
  overflow-y: auto;
  flex: 1;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 加载和空状态样式 */
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-icon, .empty-icon {
  margin-bottom: 16px;
  color: #909399;
}

.loading-text, .empty-text {
  font-size: 16px;
  color: #909399;
  margin: 0 0 20px 0;
}

.loading-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 全局样式确保对话框固定 */
.add-resident-dialog.el-dialog__wrapper {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 2000 !important;
  overflow: hidden !important;
}

/* 确保遮罩层覆盖整个屏幕 */
.add-resident-dialog .el-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 2000 !important;
}
</style>