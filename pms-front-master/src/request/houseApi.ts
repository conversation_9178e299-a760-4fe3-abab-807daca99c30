import request from "./request";
import { ReportData } from "@/types/api";

export const addHouseApi = (data: {
    userId: number;
    type: string;
    area: number;
    building: string;
    status: string;
    rent: number;
    conditions: string;
    room: string;
}): Promise<ReportData<any>> => {
    return request.post('/house/add', data)
}

export const getHouseListApi = (data: {
    pageNum: number;
    pageSize: number;
    searchKey: string;
    userId?: number;
    type?: string;
    area?: number;
    building: string;
    status?: string;
    rent?: number;
    conditions?: string;
    room?: string;
}): Promise<ReportData<any>> => {
    return request.post('/house/list', data)
}

export const getStatisticalStatus = (): Promise<ReportData<{
    data: Array<{
        name: string;
        value: number;
    }>
}>> => {
    return request.get('/house/getStatisticalStatus')
}

export const editHouseApi = (data: {
    houseId: number;
    userId: number;
    building: string;
    room: string;
    rent: number;
    conditions: string;
    area: number;
    type: string;
    status: string;
}): Promise<ReportData<any>> => {
    return request.put('/house/edit', data)
}

export const deleteHouseApi = (data: {
    id: number;
}): Promise<ReportData<any>> => {
    return request.delete('/house/delete', { data })
}