import request from "./request";
import { ReportData } from "@/types/api";

export const addResidentApi = (data: {
    userId: number;
    houseId: number;
    name: string;
    phone: string;
    email: string;
    status: string;
    moveInDate: Date;
    endDate: Date;
    rent: number;
    deposit: number;
    createTime: Date;
    updateTime: Date;
}): Promise<ReportData<any>> => {
    return request.post('/resident/add', data)
}

export const editResidentApi = (data: {
    residentId: number;
    userId: number;
    houseId: number;
    name: string;
    phone: string;
    email: string;
    status: string;
    moveInDate: Date;
    endDate: Date;
    rent: number;
    deposit: number;
}): Promise<ReportData<any>> => {
    return request.put('/resident/edit', data)
}

export const deleteResidentApi = (residentId: number): Promise<ReportData<any>> => {
    return request.delete(`/resident/delete/${residentId}`)
}

export const getResidentByIdApi = (residentId: number): Promise<ReportData<any>> => {
    return request.get(`/resident/getResidentById/${residentId}`)
}

export const getResidentListApi = (data: {
    pageNum: number;
    pageSize: number;
    searchKey: string;
    userId?: number;
    houseId?: number;
    name?: string;
    phone: string;
    email: string;
    status: string;
    moveInDate: Date;
    endDate: Date;
    rent: number;
    deposit: number;
}): Promise<ReportData<any>> => {
    return request.post('/resident/list', data)
}